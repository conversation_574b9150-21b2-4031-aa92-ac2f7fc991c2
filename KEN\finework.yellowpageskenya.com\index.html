<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>Finework Kenya Limited: Trusted Experts in Fire Protection and Building Services</title>
  <meta name="description"
    content="Reliable fire safety solutions: alarms, suppression, hydrants, sprinklers, extinguisher services & training. Protect lives - contact us today!">


  <meta name="keywords" content="Finework Kenya Limited">

  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <!-- Favicon -->
  <link rel="icon" href="./favicon/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="./favicon/apple-touch-icon.jpg">
  <link rel="icon" type="image/jpg" sizes="32x32" href="./favicon/favicon-32x32.jpg">
  <link rel="icon" type="image/jpg" sizes="16x16" href="./favicon/favicon-16x16.jpg">
  <link rel="manifest" href="./favicon/site.webmanifest.json">
  <!-- Robots -->
  <meta name="robots" content="index, follow">
  <!-- Site Published Date -->
  <meta property="article:published_time" content="2025-06-12">
  <!-- Google Verification -->
  <!-- <meta name="google-site-verification" content="Your Google Search Console Verification Code"> -->
   <meta name="google-site-verification" content="N0HompzJ25uz2PejDBXT-26yePXEWujgsnsWOcVrNQQ">
 
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://finework.yellowpageskenya.com">
  <meta property="og:title" content="Finework Kenya Limited: Trusted Experts in Fire Protection and Building Services">
  <meta property="og:description"
    content="Reliable fire safety solutions: alarms, suppression, hydrants, sprinklers, extinguisher services & training. Protect lives - contact us today!">
  <meta property="og:image" content="https://finework.yellowpageskenya.com/img/log.webp">
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:site" content="@yellowpages254">
  <meta property="twitter:url" content="https://finework.yellowpageskenya.com ">
  <meta property="twitter:title" content="Finework Kenya Limited: Trusted Experts in Fire Protection and Building Services ">
  <meta property="twitter:description"
    content="Reliable fire safety solutions: alarms, suppression, hydrants, sprinklers, extinguisher services & training. Protect lives - contact us today!">
  <meta property="twitter:image" content="https://finework.yellowpageskenya.com/img/log.webp">
  <!-- Canonical URL -->
  <link rel="canonical" href="https://finework.yellowpageskenya.com">
  <!-- Hreflang tags -->
  <link rel="alternate" hreflang="en" href="https://finework.yellowpageskenya.com">
  <!-- Include more hreflang tags here if you have the website available in other languages -->
  <!-- Sitemap -->
  <link rel="sitemap" type="application/xml" title="Sitemap"
    href="https://finework.yellowpageskenya.com/sitemap.xml">

  <!-- Preconnect to Google Maps APIs -->
  <link rel="preconnect" href="https://maps.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://maps.gstatic.com" crossorigin>

  <!-- <link rel="preload" as="image" href="./img/hero_bg.webp"> -->

  <!-- Internal CSS -->

  <link rel="stylesheet" href="css/features.css">
  <link rel="stylesheet" href="css/ots.css">
  <link rel="stylesheet" href="css/s2.css">
  <link rel="stylesheet" href="css/hero.css">
  <link rel="stylesheet" href="css/service-section.css">
  <link rel="stylesheet" href="css/mn.css">
  <link rel="stylesheet" href="css/about.css">
  <link rel="stylesheet" href="css/main.css">
  <link rel="stylesheet" href="css/services.css">
  <link rel="stylesheet" href="css/testimonial.css">
   


  <!-- <link rel="alternate" hreflang="en" href="https://finework.yellowpageskenya.com/"> -->
  <link rel="alternate" hreflang="x-default" href="https://finework.yellowpageskenya.com/">

<!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-7ZC1D2KZ2Q"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-7ZC1D2KZ2Q');
</script>

 

  <style>
    html {
      scroll-behavior: smooth;
    }

    /* Inline critical font styles for Poppins */
    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 400;
      src: local('Poppins Regular'), local('Poppins-Regular'), url(https://fonts.gstatic.com/s/poppins/v15/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 700;
      src: local('Poppins Bold'), local('Poppins-Bold'), url(https://fonts.gstatic.com/s/poppins/v15/pxiByp8kv8JHgFVrLCz7Z1xlFd2JQEk.woff2) format('woff2');
      font-display: swap;
    }

    /* Inline critical font styles for Work Sans */

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 400;
      src: local('Work Sans Regular'),
        local('WorkSans-Regular'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 600;
      src: local('Work Sans SemiBold'),
        local('WorkSans-SemiBold'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 700;
      src: local('Work Sans Bold'),
        local('WorkSans-Bold'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }


    body {
      font-family: 'Work Sans', sans-serif;
    }

    .mobile-menu {
      transition: transform 0.3s ease-in-out;
    }

    .mobile-menu.hidden {
      transform: translateX(-100%);
    }

    #top-bar {
      transition: transform 0.3s ease-out, opacity 0.3s ease-out;
    }

    #main-nav {
      transition: all 0.3s ease-out;
    }

    .sticky {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 50;
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  </style>

</head>

<body>
  <div class="content-grid" data-theme="blue">
    <div id="top-bar">
      <div class="top-bar-inner">
        <div class="contact-info-topbar">
          <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
              <polyline points="22,6 12,13 2,6"></polyline>
            </svg>
           <EMAIL>
          </a>
          <span class="separator"></span>
          <span class="working-hours">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
             Mon - Fri: 8:00am – 5:00pm 
          </span>
        </div>
        <div class="phone-and-social">
          <div class="phone-number phon">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
              stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
              <path
                d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z">
              </path>
            </svg>
            <a href="tel:0746776040">0746776040 / 0754006060</a>
          </div>
          <div class="social-icons">
            <!-- Twitter -->
            <a href="https://x.com/FineworkKenya" target="_blank" class="social-icon" aria-label="Twitter">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                <g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                  color="currentColor">
                  <path
                    d="M2.5 12c0-4.478 0-6.718 1.391-8.109S7.521 2.5 12.001 2.5c4.478 0 6.717 0 8.108 1.391S21.5 7.521 21.5 12c0 4.478 0 6.718-1.391 8.109S16.479 21.5 12 21.5c-4.478 0-6.717 0-8.109-1.391c-1.39-1.392-1.39-3.63-1.39-8.109" />
                  <path
                    d="m7 17l4.194-4.193M17 7l-4.193 4.194m0 0L9.777 7H7l4.194 5.807m1.613-1.614L17 17h-2.778l-3.028-4.193" />
                </g>
              </svg>
            </a>

            <!-- linkedin-->
            <a href="https://www.linkedin.com/company/fineworkkenyaltd/posts/?feedView=all" target="_blank" class="social-icon" aria-label="linkedin">

            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M18.44 3.06H5.56a2.507 2.507 0 0 0-2.5 2.5v12.88a2.507 2.507 0 0 0 2.5 2.5h12.88a2.5 2.5 0 0 0 2.5-2.5V5.56a2.5 2.5 0 0 0-2.5-2.5m1.5 15.38a1.51 1.51 0 0 1-1.5 1.5H5.56a1.51 1.51 0 0 1-1.5-1.5V5.56a1.51 1.51 0 0 1 1.5-1.5h12.88a1.51 1.51 0 0 1 1.5 1.5Z"/><path fill="currentColor" d="M6.376 10.748a1 1 0 1 1 2 0v6.5a1 1 0 0 1-2 0Z"/><circle cx="7.376" cy="6.744" r="1" fill="currentColor"/><path fill="currentColor" d="M17.62 13.37v3.88a1 1 0 1 1-2 0v-3.88a1.615 1.615 0 1 0-3.23 0v3.88a1 1 0 0 1-2 0v-6.5a1.016 1.016 0 0 1 1-1a.94.94 0 0 1 .84.47a3.61 3.61 0 0 1 5.39 3.15"/></svg></a>
          
            <!-- Facebook-->
            <a href="https://www.facebook.com/profile.php?id=61577295100278" target="_blank" class="social-icon" aria-label="Facebook">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 16 16"><path fill="currentColor" fill-rule="evenodd" d="M8.5 13.478a5.5 5.5 0 1 0-1.5-.069V9.75H5.75a.75.75 0 0 1 0-1.5H7V7.24c0-.884.262-1.568.722-2.032S8.843 4.5 9.644 4.5c.273 0 .612.04.948.213a.75.75 0 0 1-.685 1.334A.6.6 0 0 0 9.644 6c-.493 0-.737.144-.857.265c-.12.12-.287.39-.287.975v1.01h1.25a.75.75 0 0 1 0 1.5H8.5zM8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14" clip-rule="evenodd"></path></svg>
              </svg>
            </a>

           

            <a href="https://www.instagram.com/fineworkkenya_ltd/" target="_blank" class="social-icon" aria-label="Instagram">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                <path fill="currentColor" d="M16 7a1 1 0 1 1 2 0a1 1 0 0 1-2 0"></path>
                <path fill="currentColor" fill-rule="evenodd" d="M12 7.25a4.75 4.75 0 1 0 0 9.5a4.75 4.75 0 0 0 0-9.5M8.75 12a3.25 3.25 0 1 1 6.5 0a3.25 3.25 0 0 1-6.5 0" clip-rule="evenodd"></path>
                <path fill="currentColor" fill-rule="evenodd" d="M17.258 2.833a47.7 47.7 0 0 0-10.516 0c-2.012.225-3.637 1.81-3.873 3.832a46 46 0 0 0 0 10.67c.236 2.022 1.86 3.607 3.873 3.832a47.8 47.8 0 0 0 10.516 0c2.012-.225 3.637-1.81 3.873-3.832a46 46 0 0 0 0-10.67c-.236-2.022-1.86-3.607-3.873-3.832m-10.35 1.49a46.2 46.2 0 0 1 10.184 0c1.33.15 2.395 1.199 2.55 2.517a44.4 44.4 0 0 1 0 10.32a2.89 2.89 0 0 1-2.55 2.516a46.2 46.2 0 0 1-10.184 0a2.89 2.89 0 0 1-2.55-2.516a44.4 44.4 0 0 1 0-10.32a2.89 2.89 0 0 1 2.55-2.516" clip-rule="evenodd"></path>
              </svg>
            </a>

            <a href="https://www.tiktok.com/@fineworkkenya" target="_blank" class="social-icon" aria-label="TikTok">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                <g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5">
                  <path d="M21 8v8a5 5 0 0 1-5 5H8a5 5 0 0 1-5-5V8a5 5 0 0 1 5-5h8a5 5 0 0 1 5 5"></path>
                  <path d="M10 12a3 3 0 1 0 3 3V6c.333 1 1.6 3 4 3"></path>
                </g>
              </svg>
            </a>

             <!-- Whatsapp -->
             <a href="https://api.whatsapp.com/send/?phone=0746776040&amp;text&amp;type=phone_number&amp;app_absent=0" target="_blank" class="social-icon" aria-label="whatsapp">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                <g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" color="currentColor">
                  <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12c0 1.379.28 2.693.784 3.888c.279.66.418.99.436 1.24c.017.25-.057.524-.204 1.073L2 22l3.799-1.016c.549-.147.823-.22 1.073-.204c.25.018.58.157 1.24.436A10 10 0 0 0 12 22"></path>
                  <path d="m8.588 12.377l.871-1.081c.367-.456.82-.88.857-1.488c.008-.153-.1-.841-.315-2.218C9.916 7.049 9.41 7 8.973 7c-.57 0-.855 0-1.138.13c-.358.163-.725.622-.806 1.007c-.064.305-.016.515.079.935c.402 1.783 1.347 3.544 2.811 5.009c1.465 1.464 3.226 2.409 5.01 2.811c.42.095.629.143.934.079c.385-.08.844-.448 1.008-.806c.129-.283.129-.568.129-1.138c0-.438-.049-.943-.59-1.028c-1.377-.216-2.065-.323-2.218-.315c-.607.036-1.032.49-1.488.857l-1.081.87"></path>
                </g>
              </svg>
            </a>
           
            
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Navigation -->
  <nav id="main-nav" class="content-grid dark-theme">
    <div class="nav-inner">
      <div class="logo">
        <!-- <img src="./img/logo.webp" alt="Logo" title="Logo" width="190" height="53"> -->

  <img
  src="./img/log.webp"
  srcset="./img/log.webp 1x, ./img/log2x.webp 2x"
  alt="Logo"
  title="Logo"
  width="276"
  height="60"
>

      </div>
      <div class="desktop-menu">
        <a href="/">Home</a>
        <a href="#about">About Us</a>
        <a href="#products">Products</a>
        <a href="#services">Services</a>
        <a href="#contact">Contact</a>
      </div>
      <button id="mobile-menu-toggle" class="mobile-menu-toggle">&#9776;</button>
      <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya" class="contact-btn">Get
        In Touch</a>
    </div>
  </nav>

  <!-- Mobile Menu Overlay -->
  <div id="mobile-menu" class="mobile-menu">
    <div class="mobile-menu-content">
      <div class="menu-header">
        <!-- <img src="./img/logo.webp" alt="Logo" title="Logo"> -->


<img
  src="./img/log.webp"
  srcset="./img/log.webp 1x, ./img/log2x.webp 2x"
  alt="Logo"
  title="Logo"
  width="276"
  height="60"
>

        <button id="close-mobile-menu">&times;</button>
      </div>
      <div class="menu-links">
        <a href="/">Home</a>
        <a href="#about">About Us</a>
        <a href="#products">Products</a>
        <a href="#services">Services</a>
        <a href="#contact">Contact</a>

        <!-- button mobile menu -->
        <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya"
          class="contact-btn">Get In Touch</a>
      </div>
    </div>
  </div>



<section class="hero-slider">
  <!-- Slide 1 -->
  <div class="slide active">
    <div class="slide-bg" style="background-image: url('./img/slider_9.webp');"></div>
    <div class="slide-overlay"></div>
    <div class="text-overlay"></div>
    <div class="content-grid">
      <div class="hero-content">
        <span class="h6"> Reliable Fire Protection Solutions</span>
        <h1>Complete Fire Safety from Start to Finish</h1>
        <p>From alarms to extinguishers, we deliver end-to-end fire protection solutions with precision, care, and reliability.</p>
        <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya" class="explore-btn">Get Consultation</a>
      </div>
    </div>
  </div>
  
  <!-- Slide 2 -->
  <div class="slide">
    <div class="slide-bg" style="background-image: url('./img/slider_8.webp');"></div>
    <div class="slide-overlay"></div>
    <div class="text-overlay"></div>
    <div class="content-grid">
      <div class="hero-content">
        <span class="h6">Advanced Fire & Building Services</span>
        <div class="ht">Built for Safety, Engineered for Excellence</div>
        <p>We install and maintain fire alarms, hydrants, sprinklers, and suppression systems to safeguard your spaces.</p>
        <a href="#services" class="explore-btn">View Services</a>
      </div>
    </div>
  </div>
  
  <!-- Slide 3 -->
  <div class="slide">
    <div class="slide-bg" style="background-image: url('./img/slider_7.webp');"></div>
    <div class="slide-overlay"></div>
    <div class="text-overlay"></div>
    
    <div class="content-grid">
      <div class="hero-content">
        <span class="h6">Professional Fire Safety Services</span>
        <div class="ht"> Built for Safety, Driven by Trust</div>
        <p>Our solutions are trusted across industries for their quality, precision, and reliability—keeping you safe and your systems up to standard.
      
      </p>
        <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya" class="explore-btn">Contact Us </a>
      </div>
    </div>
  </div>

  <!-- Slide 4 - NEW SLIDE -->
  <div class="slide">
    <div class="slide-bg placeholder-bg" style="background-image: url('./img/slider_3.webp');"></div>
    <div class="slide-overlay"></div>
    <div class="text-overlay"></div>
    <div class="content-grid">
      <div class="hero-content">
        <span class="h6">Inspection & Preventive Maintenance</span>
        <div class="ht">Ensuring Your Fire Systems Stay Ready</div>
        <p>Our technicians provide routine inspections and maintenance to ensure your hydrants, extinguishers, alarms, and suppressors work when it matters most.</p>
        <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya" class="explore-btn">Schedule Visit</a>
      </div>
    </div>
  </div>

  
  
  <!-- Slider controls -->
  <div class="slider-controls">
    <div class="slider-dot active" data-index="0"></div>
    <div class="slider-dot" data-index="1"></div>
    <div class="slider-dot" data-index="2"></div>
    <div class="slider-dot" data-index="3"></div>
  </div>
  
  <!-- Slider arrows -->
  <div class="slider-arrows">
    <div class="arrow prev">
      <svg viewBox="0 0 24 24">
        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"></path>
      </svg>
    </div>
    <div class="arrow next">
      <svg viewBox="0 0 24 24">
        <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"></path>
      </svg>
    </div>
  </div>
</section>



  

  <section class="features">
    <div class="pattern-overlay"></div>
    <div class="feature-container">
    <div class="content-grid">
      <div class="grid-container">
        <div class="text-content">
          <h2 class="animate-text">Core Values</h2>
          <p class="animate-text-delay">At Finework Kenya Limited, our values guide every solution. We prioritize safety, embrace innovation, and commit to excellence, integrity, and client focus.</p>
        </div>
        <div class="cards-container">
        
          <div class="card" data-tilt>
            <div class="card-header">
              <div class="card-icon">
            
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24"  viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                </svg>
  
              </div>
              <h3>Mission</h3>
            </div>
            <p>We provide innovative and reliable fire protection and mechanical solutions to enhance safety and efficiency.
</p>
            <div class="card-overlay"></div>
          </div>
  
  
          <div class="card" data-tilt>
            <div class="card-header">
              <div class="card-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z" />
                </svg>
              </div>
              <h3>Vision</h3>
            </div>
            <p>To be Kenya’s leading fire protection and building service provider, ensuring safer communities and resilient infrastructure.
</p>
            <div class="card-overlay"></div>
          </div>
  
          <div class="card" data-tilt>
            <div class="card-header">
              <div class="card-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke="currentColor">
                  <path d="M6.429 9.75 2.25 12l4.179 2.25m0-4.5 5.571 3 5.571-3m-11.142 0L2.25 7.5 12 2.25l9.75 5.25-4.179 2.25m0 0L21.75 12l-4.179 2.25m0 0 4.179 2.25L12 21.75 2.25 16.5l4.179-2.25m11.142 0-5.571 3-5.571-3" />
                </svg>
              </div>
              <h3>Core Values</h3>
            </div>
            <ul class="custom-list">
              <li>
               
  
               <span> <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24"  viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                </svg>
  
                Safety First 
 </span>
              </li>
              <li>
               
               <span> <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24"  viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                </svg>
  
                Excellence 
</span>
              </li>
              <li>
              
  
               <span><svg xmlns="http://www.w3.org/2000/svg" fill="none" width="34" height="34"  viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                </svg> Integrity 
 </span> 
              </li>
                  <li>
              
  
               <span><svg xmlns="http://www.w3.org/2000/svg" fill="none" width="34" height="34"  viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                </svg> Innovation 
 
               </span> 
              </li>
                  <li>
              
  
               <span><svg xmlns="http://www.w3.org/2000/svg" fill="none" width="34" height="34"  viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                </svg> Customer Focus 
                </span> 
              </li>


              <li>
              

               </li>
  
             
             
            </ul>
            <div class="card-overlay"></div>
          </div>
          
        </div>
      </div>
    </div>
    </div>
    
  </section>




  <div class="about-us" id="about">
    <div class="content-grid">
      <!-- <h2 class="section-title">ABOUT US</h2> -->

      <div class="about-content">
        <div class="about-image">
          <img src="./img/about_image.webp" title="Finework Kenya Limited about image"
            alt="Finework Kenya Limited about image" loading="lazy" width="635" height="613">
        </div>
        <div class="about-text">
          <span class="abt">About Us</span>



          <p>
           At Finework Kenya Limited, we are passionate about protecting lives and property through reliable fire safety and industrial solutions. Since our founding in 2017, we’ve become a trusted name in Kenya for cutting-edge fire protection systems, mechanical works, and expert consultancy in building services.


          </p>

          <p>
          Headquartered in Nairobi, our team delivers top-notch services including the installation of fire alarms, hydrant and sprinkler systems, hose reels, and suppression systems—ensuring safety from start to finish. We also provide essential services like extinguisher testing, recharging, inspection, fire safety training, and project supervision.



          </p>

          <p>With over 400 successful projects and more than 300 happy clients, we’re proud to be setting the benchmark for safety and service excellence in the region. At Finework Kenya Limited, your safety is our mission. Contact us today!


          </p>

          <div class="about-services-container">
            <ul>

                <li>
                <span><svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24"
                    viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                  </svg></span>
        Dry Powder Fire Extinguishers

              </li>
              
              <li>
                <span><svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24"
                    viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                  </svg></span>
               CO₂ Fire Extinguishers


              </li>
            

               <li>
                <span><svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24"
                    viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round"
                      d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                  </svg></span>
             Foam Fire Extinguishers


              </li>

              

            </ul>

             <ul>
              <li>
               <svg class="tick-icon"  xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24"  viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                </svg>
           

               Water-Based Fire Extinguishers
              </li>
              <li>
               <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24"  viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                </svg>
             Wet Chemical Extinguishers
              </li>
         
              
            </ul>
         
          </div>

          <a href="#services" target="_blank"  class="cta-button">View More</a>
        </div>
      </div>
    </div>
  </div>


<div class="fireprod-header" id="products">
  <h2 class="fireprod-section-title">Products</h2>

  <div class="content-grid">
    <div class="fireprod-grid">
      <div class="fireprod-intro">
        <h2 class="fireprod-heading">Firefighting Equipment (Portable)</h2>
        <!-- <span class="fireprod-divider"></span> -->
        <p class="fireprod-description">We supply a variety of portable fire extinguishers suitable for different types of fires (A, B, C, D & K), including:</p>

        <div class="fireprod-icons">
          <a href="#" class="fireprod-icon" aria-label="Plane"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 32 32"><g fill="currentColor"><path d="M18.07 16h-4.13a.95.95 0 0 1-.94-.951V12.95a.95.95 0 0 1 .94-.951h4.12c.516 0 .94.43.94.951v2.098a.935.935 0 0 1-.93.951"/><path d="M23.001 4H18v.156l4.7 1.949a.453.453 0 0 1 .256.616a.52.52 0 0 1-.676.234L18 5.18v1.863c3.38.382 6 3.247 6 6.73v14.246A2.977 2.977 0 0 1 21.02 31H6.5c-.43 0-.6-.41-.5-.83L8 23V11c0-4.943 4.032-9 9-9h5.991a1 1 0 1 1 .01 2M22 13.773A4.767 4.767 0 0 0 17.23 9h-2.46A4.767 4.767 0 0 0 10 13.773V21h12zM10 23s1.107 3.957 1.675 6h9.345c.544 0 .98-.439.98-.98V22H10zm3.014-17.736a7.1 7.1 0 0 0-2.567 3.294a6.74 6.74 0 0 1 3.319-1.484a2 2 0 0 1-.752-1.81m1.98 1.136a1.005 1.005 0 0 0 .681-1.747l-.67.757V4.4A1 1 0 0 0 14 5.4c0 .556.437 1 .995 1"/></g></svg></a>
          <a href="#" class="fireprod-icon" aria-label="Ship"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 32 32"><path fill="currentColor" d="M16 4c-1.292 0-2.394.844-2.813 2H12c-2.745 0-5 2.255-5 5h2c0-1.655 1.345-3 3-3h1v2.47c-.32.237-.733.575-1.22 1.06C10.954 12.36 10 13.5 10 15v13h12V15c0-1.5-.953-2.64-1.78-3.47c-.487-.485-.9-.823-1.22-1.06v-.283l4.844.813l1.156.188V4.812L23.844 5l-5.094.844C18.292 4.77 17.234 4 16 4m0 2c.555 0 1 .445 1 1v3h-2V7c0-.555.445-1 1-1m7 1.188v1.625l-4-.688v-.25l4-.688zM14.375 12h3.25c.15.105.578.39 1.156.97c.673.67 1.22 1.53 1.22 2.03v11h-8V15c0-.5.547-1.36 1.22-2.03c.577-.58 1.004-.865 1.155-.97zM14 17v2h4v-2z"/></svg></a>
          <a href="#" class="fireprod-icon" aria-label="Truck"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M6.975 18v-6.025l.25-.975h9.5l.25.975V18zM9 13v3h6v-3zm2.975-7.025q.425 0 .713-.288t.287-.712t-.287-.712t-.713-.288t-.712.288t-.288.712t.288.713t.712.287M16 5.55V4.4l-1.05.2q.************8v.375q0 .087-.025.187zm-9.025 6.425q0-1.575.875-2.825t2.275-1.8q-.275-.2-.487-.462T9.275 6.3L5.4 5.55q-.175-.05-.288-.175T5 5.075v-.2q0-.175.113-.3T5.4 4.4l3.875-.75q.375-.75 1.088-1.225t1.612-.475q.575 0 1.1.225T14 2.75l2.8-.55q.475-.1.838.2t.362.775V6.75q0 .475-.363.788t-.837.212L14 7.2q-.05.05-.088.075t-.087.075q1.4.55 2.275 1.8t.875 2.825H15q0-1.25-.875-2.112T12 9t-2.125.863T9 11.975zm2 10.025q-.825 0-1.412-.587T6.975 20v-2H9v2h6v-2h1.975v2q0 .825-.587 1.413T14.975 22z"/></svg></a>
      
          
        </div>
      </div>

      <div class="fireprod-card">
        <img src="./img/powder.webp" alt="Dry Powder Fire Extinguishers" title="Dry Powder Fire Extinguishers" width="426" height="330" loading="lazy">
        <div class="fireprod-content">
          <p class="fireprod-text">Dry Powder Fire Extinguishers</p>
        </div>
      </div>

      <div class="fireprod-card">
        <img src="./img/co2.webp" alt="CO₂ Fire Extinguishers" title="CO₂ Fire Extinguishers" width="426" height="330" loading="lazy">
        <div class="fireprod-content">
          <p class="fireprod-text">CO₂ Fire Extinguishers</p>
        </div>
      </div>

      <div class="fireprod-card">
        <img src="./img/foam.webp" alt="Foam Fire Extinguishers" title="Foam Fire Extinguishers" width="426" height="330" loading="lazy">
        <div class="fireprod-content">
          <p class="fireprod-text">Foam Fire Extinguishers</p>
        </div>
      </div>

      <div class="fireprod-card">
        <img src="./img/water.webp" alt="Wet Chemical Extinguishers" title="Water-Based Fire Extinguishers" width="426" height="330" loading="lazy">
        <div class="fireprod-content">
          <p class="fireprod-text">Water-Based Fire Extinguishers</p>
        </div>
      </div>

      <div class="fireprod-card">
        <img src="./img/wet-chemicals.webp" alt="Wet Chemical Extinguishers" title="Wet Chemical Extinguishers" width="426" height="330" loading="lazy">
        <div class="fireprod-content">
          <p class="fireprod-text">Wet Chemical Extinguishers</p>
        </div>
      </div>
      
      <div class="fireprod-card">
        <img src="./img/Kitchen Suppression.webp" alt="Kitchen Suppression" title="Kitchen Suppression" width="426" height="330" loading="lazy">
        <div class="fireprod-content">
          <p class="fireprod-text">Kitchen Suppression</p>
        </div>
      </div>
      
      <div class="fireprod-card">
        <img src="./img/fire_Alarm_System2.webp" alt="Fire Alarm System" title="Fire Alarm System" width="426" height="330" loading="lazy">
        <div class="fireprod-content">
          <p class="fireprod-text">Fire Alarm System</p>
        </div>
      </div>
      
      <div class="fireprod-card">
        <img src="./img/Fire_Pump.webp" alt="Fire Pump" title="Fire Pump" width="426" height="330" loading="lazy">
        <div class="fireprod-content">
          <p class="fireprod-text">Fire Pump</p>
        </div>
      </div>
      
      <div class="fireprod-card">
        <img src="./img/Hydrant_System_p.webp" alt="Hydrant System" title="Hydrant System" width="426" height="330" loading="lazy">
        <div class="fireprod-content">
          <p class="fireprod-text">Hydrant System</p>
        </div>
      </div>
      
      <div class="fireprod-card">
        <img src="./img/Hose_Reel.webp" alt="Hose Reel" title="Hose Reel" width="426" height="330" loading="lazy">
        <div class="fireprod-content">
          <p class="fireprod-text">Hose Reel</p>
        </div>
      </div>

      
      <div class="fireprod-card">
        <img src="./img/automatic_extinguisher.webp" alt="Automatic Extinguishers" title="Automatic Extinguishers" width="426" height="330" loading="lazy">
        <div class="fireprod-content">
          <p class="fireprod-text">Automatic Extinguishers</p>
        </div>
      </div>
    </div>
  </div>
</div>





    <div class="content-grid" id="services">
        <div class="services-header">
            <h2 class="products-section-title">Our Services</h2>
        </div>
        <div class="services-grid">
            <div class="service-item">
                <div class="service-image">
                    <img src="./img/Fire-alarm.webp" alt="Fire Alarm System Installation" title="Fire Alarm System Installation" loading="lazy" width="417" height="313">
                </div>
                <div class="service-content">
                    <h3>Fire Alarm System Installation
</h3>
                    
                   
                </div>
              
            </div>
              <div class="service-item">
                <div class="service-image">
                    <img src="./img/Fire_Suppression_System.webp" alt="Fire Suppression System Installation" title="Fire Suppression System Installation" loading="lazy" width="417" height="313">
                </div>
                <div class="service-content">
                    <h3>Fire Suppression System Installation
</h3>
                    
                    
                </div>
                
            </div>
            <div class="service-item">
                <div class="service-image">
                    <img src="./img/Hydrant_System_Installation.webp" alt="Hydrant System Installation" title="Hydrant System Installation" loading="lazy" width="417" height="313">
                </div>
                <div class="service-content">
                    <h3>Hydrant System Installation
</h3>
                    
                  
                </div>
                
            </div>

          
         
            <div class="service-item">
                <div class="service-image">
                    <img src="./img/Sprinkler_System.webp" alt="Sprinkler System Installation" title="Sprinkler System Installation" loading="lazy" width="417" height="313">
                </div>
                <div class="service-content">
                    <h3>Sprinkler System Installation / Spares
</h3>
                   
                   
                </div>
                
            </div>

               <div class="service-item">
                <div class="service-image">
                    <img src="./img/Hose_Reel_System_Installation2.webp" alt="Hose Reel System Installation" title="Hose Reel System Installation" loading="lazy" width="417" height="313">
                </div>
                <div class="service-content">
                    <h3>Hose Reel System Installation</h3>
                    
                
                </div>
                
            </div>


            <div class="service-item">
                <div class="service-image">
                    <img src="./img/Inspection_Maintenance.webp" alt="Inspection & Maintenance" title="Inspection & Maintenance" loading="lazy" width="417" height="313">
                </div>
                
                <div class="service-content">
                    <h3>Inspection & Maintenance</h3>
                    
                </div>
               
            </div>

                <div class="service-item">
                <div class="service-image">
                    <img src="./img/Pressure_Testing.webp" alt="Pressure Testing of Fire Extinguishers" title="Pressure Testing of Fire Extinguishers" loading="lazy" width="417" height="313">
                </div>
                
                <div class="service-content">
                    <h3>Pressure Testing of Fire Extinguishers
</h3>
                    
                </div>
               
            </div>

                <div class="service-item">
                <div class="service-image">
                    <img src="./img/Recharging_of_Fire_Extinguishers.webp" alt="Recharging of Fire Extinguishers" title="Recharging of Fire Extinguishers" loading="lazy" width="417" height="313">
                </div>
                
                <div class="service-content">
                    <h3>Recharging of Fire Extinguishers
</h3>
                    
                </div>
               
            </div>

                <div class="service-item">
                <div class="service-image">
                    <img src="./img/Fire_Safety_Training.webp" alt="Fire Safety Training" title="Fire Safety Training" loading="lazy" width="417" height="313">
                </div>
                
                <div class="service-content">
                    <h3>Fire Safety Training
</h3>
                    
                </div>
               
            </div>

                <div class="service-item">
                <div class="service-image">
                    <img src="./img/Design_Supervision.webp" alt="Design & Supervision" title="Design & Supervision" loading="lazy" width="417" height="313">
                </div>
                
                <div class="service-content">
                    <h3>Design & Supervision
</h3>
                    
                </div>
               
            </div>

        </div>
    </div>

<section class="testimonial-section content-grid">
  <h2 class="section-title">Testimonials</h2>

  <div class="testimonials-container">
    <div class="testimonial">
      <div class="testimonial-content">
        <div class="left-column">
          <img src="./img/peris.webp" alt="Client Image" title="client-image" class="client-image" loading="lazy" height="70" width="70">
        </div>
        <div class="right-column">
          <p class="testimonial-text">Finework Kenya Limited installed a complete fire alarm and sprinkler system in our warehouse. The team was professional, efficient, and took the time to explain everything clearly. We now feel much more secured.</p>
          <div class="client-info">
            <h3 class="client-name">Peris</h3>
            <p class="client-role">Operations Manager</p>
          </div>
          <img src="./img/quote.svg" alt="Quote" class="quote-icon" loading="lazy" title="Quote">
        </div>
      </div>
    </div>

    <div class="testimonial">
      <div class="testimonial-content">
        <div class="left-column">
          <img src="./img/mike.webp" alt="Client Image" class="client-image" loading="lazy" title="client-image" height="70" width="70">
        </div>
        <div class="right-column">
          <p class="testimonial-text">We’ve been using Fine Work Kenya for routine extinguisher maintenance and inspection services. They are always on time, and their reporting is thorough. I highly recommend them.</p>
          <div class="client-info">
            <h3 class="client-name">Mike Wakesho
</h3>
            <p class="client-role">Production Manager</p>
          </div>
          <img src="./img/quote.svg" alt="Quote" class="quote-icon" title="Quote" loading="lazy">
        </div>
      </div>
    </div>

    <div class="testimonial">
      <div class="testimonial-content">
        <div class="left-column">
          <img src="./img/grace.webp" alt="Client Image" title="client-image" loading="lazy" height="70" width="70" class="client-image">
        </div>
        <div class="right-column">
          <p class="testimonial-text">From fire safety training to installing hydrant systems, the team demonstrated deep knowledge and great customer service.”</p>
          <div class="client-info">
            <h3 class="client-name">Grace</h3>
            <p class="client-role">Quality Assurance Manager</p>
          </div>
          <img src="./img/quote.svg" alt="Quote" class="quote-icon" title="Quote" loading="lazy">
        </div>
      </div>
    </div>

    <div class="testimonial">
      <div class="testimonial-content">
        <div class="left-column">
          <img src="./img/peter.webp" alt="Client Image" class="client-image" title="client-image" loading="lazy" height="70" width="70">
        </div>
        <div class="right-column">
          <p class="testimonial-text">Finework Kenya Limited handled our office’s fire suppression system installation seamlessly. Their attention to detail and responsiveness stood out. Truly reliable.</p>
          <div class="client-info">
            <h3 class="client-name">Daniel Mwangi</h3>
            <p class="client-role">HR Manager</p>
          </div>
          <img src="./img/quote.svg" alt="Quote" class="quote-icon" title="Quote" loading="lazy">
        </div>
      </div>
    </div>
  </div>

  <div class="navigation-dots">
    <span class="dot active"></span>
    <span class="dot"></span>
    <span class="dot"></span>
    <span class="dot"></span>
  </div>
</section>


  <section class="contact" id="contact">
    <div class="content-grid">
      <h2 class="section-title">Contact Us</h2>
      <div class="contact-content">
        <div class="contact-map">
    

          <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3988.790449145602!2d36.8375511!3d-1.3005879999999996!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x182f11888da1c769%3A0x63d070a516190548!2sFINE%20WORK%20KENYA%20LTD!5e0!3m2!1sen!2ske!4v1749734533840!5m2!1sen!2ske" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade" title="Finework Kenya Limited Map"></iframe>


        </div>
        <div class="contact-info">
          <div class="contact-item">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M256 0C158.75 0 80 78.75 80 176C80 267.4 256 512 256 512C256 512 432 267.4 432 176C432 78.75 353.25 0 256 0zM256 240C220.65 240 192 211.35 192 176C192 140.65 220.65 112 256 112C291.35 112 320 140.65 320 176C320 211.35 291.35 240 256 240z">
                </path>
              </svg>
            </span>
            <div>
              <h3>Physical Location</h3>
              <p>Enterprise Center Building, 1st floor, Addis Ababa Road off Enterprise Road, Industrial Area, Nairobi Kenya
</p>
              
            </div>
          </div>
          <div class="contact-item">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="m497.39 361.8l-112-48a24 24 0 0 0-28 6.9l-49.6 60.6A370.66 370.66 0 0 1 130.6 204.11l60.6-49.6a23.94 23.94 0 0 0 6.9-28l-48-112A24.16 24.16 0 0 0 122.6.61l-104 24A24 24 0 0 0 0 48c0 256.5 207.9 464 464 464a24 24 0 0 0 23.4-18.6l24-104a24.29 24.29 0 0 0-14.01-27.6">
                </path>
              </svg>
            </span>
            <div>
              <h3>Phone number</h3>
              <p><a href="tel:0746776040">0746776040 </a></p>

              <p><a href="tel:0754006060">0754006060 </a></p>
            </div>
          </div>
          <div class="contact-item">
            <span> <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7c22.4 17.4 52.1 39.5 154.1 113.6c21.1 15.4 56.7 47.8 92.2 47.6c35.7.3 72-32.8 92.3-47.6c102-74.1 131.6-96.3 154-113.7M256 320c23.2.4 56.6-29.2 73.4-41.4c132.7-96.3 142.8-104.7 173.4-128.7c5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9c30.6 23.9 40.7 32.4 173.4 128.7c16.8 12.2 50.2 41.8 73.4 41.4">
                </path>
              </svg></span>
            <div>
              <h3>Email</h3>
              <p><a
                  href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya"><EMAIL></a>
              </p>
            </div>
          </div>
          <div class="contact-item">
            <span><svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M256 0a256 256 0 1 1 0 512a256 256 0 1 1 0-512m-24 120v136c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2V120c0-13.3-10.7-24-24-24s-24 10.7-24 24">
                </path>
              </svg></span>
            <div>
              <h3>Operating Hours</h3>
              <p>Monday to Friday: 8:00am – 5:00pm 
</p>
            </div>
          </div>
          <!-- Social Media Icons -->
          <div class="contact-item">


            <span><svg class="contact-icon" xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem"
                viewBox="0 0 16 16">
                <path fill="currentColor"
                  d="M12 10c-.8 0-1.4.3-2 .8L6.8 9c.1-.3.2-.7.2-1s-.1-.7-.2-1L10 5.2c.6.5 1.2.8 2 .8c1.7 0 3-1.3 3-3s-1.3-3-3-3s-3 1.3-3 3v.5L5.5 5.4C5.1 5.2 4.6 5 4 5C2.4 5 1 6.3 1 8c0 1.6 1.4 3 3 3c.6 0 1.1-.2 1.5-.4L9 12.5v.5c0 1.7 1.3 3 3 3s3-1.3 3-3s-1.3-3-3-3" />
              </svg></span>

            <div>
              <h3>Connect With Us</h3>


              <div class="social-links">
                <!-- Twitter -->
                <a href="https://x.com/FineworkKenya" target="_blank" class="social-link" aria-label="Twitter">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"
                      stroke-width="1.5" color="currentColor">
                      <path
                        d="M2.5 12c0-4.478 0-6.718 1.391-8.109S7.521 2.5 12.001 2.5c4.478 0 6.717 0 8.108 1.391S21.5 7.521 21.5 12c0 4.478 0 6.718-1.391 8.109S16.479 21.5 12 21.5c-4.478 0-6.717 0-8.109-1.391c-1.39-1.392-1.39-3.63-1.39-8.109" />
                      <path
                        d="m7 17l4.194-4.193M17 7l-4.193 4.194m0 0L9.777 7H7l4.194 5.807m1.613-1.614L17 17h-2.778l-3.028-4.193" />
                    </g>
                  </svg>
                </a>

                

                 
                <!-- linkedIn -->
                <a href="https://www.linkedin.com/company/fineworkkenyaltd/posts/?feedView=all" target="_blank"
                  class="social-link" aria-label="linkedIn">
                 <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M18.44 3.06H5.56a2.507 2.507 0 0 0-2.5 2.5v12.88a2.507 2.507 0 0 0 2.5 2.5h12.88a2.5 2.5 0 0 0 2.5-2.5V5.56a2.5 2.5 0 0 0-2.5-2.5m1.5 15.38a1.51 1.51 0 0 1-1.5 1.5H5.56a1.51 1.51 0 0 1-1.5-1.5V5.56a1.51 1.51 0 0 1 1.5-1.5h12.88a1.51 1.51 0 0 1 1.5 1.5Z"/><path fill="currentColor" d="M6.376 10.748a1 1 0 1 1 2 0v6.5a1 1 0 0 1-2 0Z"/><circle cx="7.376" cy="6.744" r="1" fill="currentColor"/><path fill="currentColor" d="M17.62 13.37v3.88a1 1 0 1 1-2 0v-3.88a1.615 1.615 0 1 0-3.23 0v3.88a1 1 0 0 1-2 0v-6.5a1.016 1.016 0 0 1 1-1a.94.94 0 0 1 .84.47a3.61 3.61 0 0 1 5.39 3.15"/></svg>
                </a>
              
                <!-- Facebook -->
                <a href="https://www.facebook.com/profile.php?id=61577295100278" target="_blank"
                  class="social-link" aria-label="Facebook">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 16 16"><path fill="currentColor" fill-rule="evenodd" d="M8.5 13.478a5.5 5.5 0 1 0-1.5-.069V9.75H5.75a.75.75 0 0 1 0-1.5H7V7.24c0-.884.262-1.568.722-2.032S8.843 4.5 9.644 4.5c.273 0 .612.04.948.213a.75.75 0 0 1-.685 1.334A.6.6 0 0 0 9.644 6c-.493 0-.737.144-.857.265c-.12.12-.287.39-.287.975v1.01h1.25a.75.75 0 0 1 0 1.5H8.5zM8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14" clip-rule="evenodd"></path></svg>
                </a>
              

                <a href="https://www.instagram.com/fineworkkenya_ltd/" target="_blank" class="social-link" aria-label="Instagram">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <path fill="currentColor" d="M16 7a1 1 0 1 1 2 0a1 1 0 0 1-2 0"></path>
                    <path fill="currentColor" fill-rule="evenodd" d="M12 7.25a4.75 4.75 0 1 0 0 9.5a4.75 4.75 0 0 0 0-9.5M8.75 12a3.25 3.25 0 1 1 6.5 0a3.25 3.25 0 0 1-6.5 0" clip-rule="evenodd"></path>
                    <path fill="currentColor" fill-rule="evenodd" d="M17.258 2.833a47.7 47.7 0 0 0-10.516 0c-2.012.225-3.637 1.81-3.873 3.832a46 46 0 0 0 0 10.67c.236 2.022 1.86 3.607 3.873 3.832a47.8 47.8 0 0 0 10.516 0c2.012-.225 3.637-1.81 3.873-3.832a46 46 0 0 0 0-10.67c-.236-2.022-1.86-3.607-3.873-3.832m-10.35 1.49a46.2 46.2 0 0 1 10.184 0c1.33.15 2.395 1.199 2.55 2.517a44.4 44.4 0 0 1 0 10.32a2.89 2.89 0 0 1-2.55 2.516a46.2 46.2 0 0 1-10.184 0a2.89 2.89 0 0 1-2.55-2.516a44.4 44.4 0 0 1 0-10.32a2.89 2.89 0 0 1 2.55-2.516" clip-rule="evenodd"></path>
                  </svg>
                </a>

                <a href="https://www.tiktok.com/@fineworkkenya" target="_blank" class="social-link" aria-label="TikTok">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5">
                      <path d="M21 8v8a5 5 0 0 1-5 5H8a5 5 0 0 1-5-5V8a5 5 0 0 1 5-5h8a5 5 0 0 1 5 5"></path>
                      <path d="M10 12a3 3 0 1 0 3 3V6c.333 1 1.6 3 4 3"></path>
                    </g>
                  </svg>
                </a>

  <!-- whatsapp -->
                 <a href="https://api.whatsapp.com/send/?phone=0746776040&amp;text&amp;type=phone_number&amp;app_absent=0" target="_blank" class="social-link" aria-label="Whatsapp">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" color="currentColor">
                      <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12c0 1.379.28 2.693.784 3.888c.279.66.418.99.436 1.24c.017.25-.057.524-.204 1.073L2 22l3.799-1.016c.549-.147.823-.22 1.073-.204c.25.018.58.157 1.24.436A10 10 0 0 0 12 22"></path>
                      <path d="m8.588 12.377l.871-1.081c.367-.456.82-.88.857-1.488c.008-.153-.1-.841-.315-2.218C9.916 7.049 9.41 7 8.973 7c-.57 0-.855 0-1.138.13c-.358.163-.725.622-.806 1.007c-.064.305-.016.515.079.935c.402 1.783 1.347 3.544 2.811 5.009c1.465 1.464 3.226 2.409 5.01 2.811c.42.095.629.143.934.079c.385-.08.844-.448 1.008-.806c.129-.283.129-.568.129-1.138c0-.438-.049-.943-.59-1.028c-1.377-.216-2.065-.323-2.218-.315c-.607.036-1.032.49-1.488.857l-1.081.87"></path>
                    </g>
                  </svg>
                </a>
              
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <footer class="footer">
    <div class="content-grid">
      <div class="footer-content">
        <div class="copyright">
          <p>&copy; <span id="current-year"></span> Finework Kenya Limited. All Rights Reserved.</p>

        </div>
        <div class="designer">
          <a href="https://www.yellowpageskenya.com/" target="_blank" rel="noopener noreferrer">
            <img src="./img/yp_logo.webp" loading="lazy" alt="Yellow Pages Kenya" width="50" height="50"
              title="Yellow Pages Kenya">
            <p>Powered by Yellow Pages Kenya.</p>
          </a>
        </div>
      </div>
    </div>
  </footer>

  <script src="./js/testimonial.js"></script>
  <!-- <script src="./js/main.js"></script> -->

  <script src="https://cdn.jsdelivr.net/npm/vanilla-tilt@latest/dist/vanilla-tilt.min.js"></script>



  <script>
    document.getElementById('current-year').textContent = new Date().getFullYear();
  </script>

  <script>
    // Initialize VanillaTilt for 3D card effect
    VanillaTilt.init(document.querySelectorAll(".card"), {
      max: 5,
      speed: 400,
      glare: true,
      "max-glare": 0.2,
    });

    // Intersection Observer for scroll animations
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = 1;
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, {
      threshold: 0.1
    });

    // Observe all cards
    document.querySelectorAll('.card').forEach(card => {
      card.style.opacity = 0;
      card.style.transform = 'translateY(20px)';
      observer.observe(card);
    });
  </script>

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const topBar = document.getElementById('top-bar');
      const mainNav = document.getElementById('main-nav');
      const mainContent = document.querySelector('body'); // Adjust this selector if needed
      const mobileMenu = document.getElementById('mobile-menu');
      const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
      const closeMobileMenu = document.getElementById('close-mobile-menu');
      const mobileMenuLinks = document.querySelectorAll('.mobile-menu .menu-links a');

      // Mobile Menu Logic
      mobileMenuToggle.addEventListener('click', () => {
        mobileMenu.classList.add('show');
      });

      closeMobileMenu.addEventListener('click', () => {
        mobileMenu.classList.remove('show');
      });

      // Add click event listeners to all mobile menu links
      mobileMenuLinks.forEach(link => {
        link.addEventListener('click', function (event) {
          mobileMenu.classList.remove('show');

          const href = this.getAttribute('href');
          if (href.startsWith('#') && href !== '#') {
            event.preventDefault();

            const targetElement = document.querySelector(href);

            if (targetElement) {
              setTimeout(() => {
                const yOffset = -80;
                const y = targetElement.getBoundingClientRect().top + window.pageYOffset + yOffset;

                window.scrollTo({
                  top: y,
                  behavior: 'smooth'
                });
              }, 300);
            }
          }
        });
      });

      // Debug function to log sticky state
      function logStickyState() {
        //console.log('Scroll position:', window.scrollY);
        //console.log('mainNav has sticky class:', mainNav.classList.contains('sticky'));
        //console.log('mainNav style:', mainNav.style.cssText);
        //console.log('computed position:', window.getComputedStyle(mainNav).position);
      }

      // Improved Sticky Header Logic
      function handleScroll() {
        const scrollTop = window.scrollY || document.documentElement.scrollTop;
        //console.log('Scrolling, position:', scrollTop);

        if (scrollTop > 50) {
          // Make sure we're applying direct styles
          mainNav.style.position = 'fixed';
          mainNav.style.top = '0';
          mainNav.style.left = '0';
          mainNav.style.width = '100%';
          mainNav.style.zIndex = '100';
          mainNav.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
          mainNav.classList.add('sticky');

          // Add padding to body to prevent content jump
          mainContent.style.paddingTop = mainNav.offsetHeight + 'px';

          // Hide the top bar
          if (topBar) {
            topBar.style.display = 'none';
          }

          logStickyState();
        } else {
          // Remove direct styles
          mainNav.style.position = '';
          mainNav.style.top = '';
          mainNav.style.left = '';
          mainNav.style.width = '';
          mainNav.style.zIndex = '';
          mainNav.style.boxShadow = '';
          mainNav.classList.remove('sticky');

          // Remove padding from body
          mainContent.style.paddingTop = '0';

          // Show the top bar on desktop
          if (topBar && window.innerWidth >= 1024) {
            topBar.style.display = 'block';
          }

          logStickyState();
        }
      }

      // Initial check on page load
      handleScroll();

      // Add scroll event listener
      window.addEventListener('scroll', handleScroll);

      // Handle window resize
      window.addEventListener('resize', () => {
        if (window.innerWidth < 1024 && topBar) {
          topBar.style.display = 'none';
        } else if (window.scrollY <= 50 && topBar) {
          topBar.style.display = 'block';
        }

        // Recalculate sticky state on resize
        handleScroll();
      });
    });
  </script>



  <script>
   
    function throttle(fn, limit) {
      let waiting = false;
      return function (...args) {
        if (!waiting) {
          fn.apply(this, args);
          waiting = true;
          setTimeout(() => waiting = false, limit);
        }
      };
    }

    function handleParallaxScroll() {
      const elements = document.querySelectorAll('[data-parallax]');
      const scrollY = window.scrollY;

      elements.forEach(el => {
        const container = el.closest('.parallax-container');
        const rect = container.getBoundingClientRect();
        const offsetTop = container.offsetTop;
        const height = container.offsetHeight;

        // Only calculate if it's in view
        if (scrollY + window.innerHeight > offsetTop && scrollY < offsetTop + height) {
          const speed = 0.5; // Adjust this to control intensity
          const yPos = (scrollY - offsetTop) * speed;
          el.style.transform = `translateY(${yPos}px)`;
        }
      });
    }

    document.addEventListener('DOMContentLoaded', function () {
      window.addEventListener('scroll', throttle(handleParallaxScroll, 16)); // 60fps-ish
    });
  </script>
  
 <script>
document.addEventListener("DOMContentLoaded", function () {
  const lazySections = document.querySelectorAll('.lazy-background');

  const observer = new IntersectionObserver(entries => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const el = entry.target;
        const bgUrl = el.dataset.bg;

        // Create a new image to preload the background
        const img = new Image();
        img.src = bgUrl;

        img.onload = function () {
          // Only set background when fully loaded
          el.style.backgroundImage = `url('${bgUrl}')`;
          el.classList.add('loaded');
        };

        // Stop observing this element
        observer.unobserve(el);
      }
    });
  }, {
    rootMargin: '200px', // Preload a bit before the element enters the viewport
    threshold: 0.1
  });

  lazySections.forEach(section => observer.observe(section));
});
</script>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Get all slides
        const slides = document.querySelectorAll('.slide');
        const dots = document.querySelectorAll('.slider-dot');
        const prevArrow = document.querySelector('.arrow.prev');
        const nextArrow = document.querySelector('.arrow.next');
        let currentSlide = 0;
        let slideInterval;
        let isAnimating = false;
        
        // Function to change slide
        function goToSlide(index) {
          if (isAnimating || currentSlide === index) return;
          
          isAnimating = true;
          
          // Remove active class from all slides and dots
          slides.forEach(slide => slide.classList.remove('active'));
          dots.forEach(dot => dot.classList.remove('active'));
          
          // Add active class to current slide and dot
          slides[index].classList.add('active');
          dots[index].classList.add('active');
          
          // Update current slide index
          currentSlide = index;
          
          // Set a timeout to prevent rapid clicking during transition
          setTimeout(() => {
            isAnimating = false;
          }, 1200); // Match this to your CSS transition time
        }
        
        // Function to go to next slide
        function nextSlide() {
          if (isAnimating) return;
          
          let nextIndex = currentSlide + 1;
          if (nextIndex >= slides.length) {
            nextIndex = 0;
          }
          goToSlide(nextIndex);
        }
        
        // Function to go to previous slide
        function prevSlide() {
          if (isAnimating) return;
          
          let prevIndex = currentSlide - 1;
          if (prevIndex < 0) {
            prevIndex = slides.length - 1;
          }
          goToSlide(prevIndex);
        }
        
        // Set up dot click events
        dots.forEach(dot => {
          dot.addEventListener('click', function() {
            if (isAnimating) return;
            goToSlide(parseInt(this.getAttribute('data-index')));
            restartSlideInterval();
          });
        });
        
        // Set up arrow click events
        nextArrow.addEventListener('click', function() {
          nextSlide();
          restartSlideInterval();
        });
        
        prevArrow.addEventListener('click', function() {
          prevSlide();
          restartSlideInterval();
        });
        
        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
          if (e.key === 'ArrowLeft') {
            prevSlide();
            restartSlideInterval();
          } else if (e.key === 'ArrowRight') {
            nextSlide();
            restartSlideInterval();
          }
        });
        
        // Function to start automatic slide transition
        function startSlideInterval() {
          slideInterval = setInterval(nextSlide, 7000); // Change slide every 7 seconds
        }
        
        // Function to restart slide interval after user interaction
        function restartSlideInterval() {
          clearInterval(slideInterval);
          startSlideInterval();
        }
        
        // Pause autoplay on hover
        const sliderContainer = document.querySelector('.hero-slider');
        sliderContainer.addEventListener('mouseenter', function() {
          clearInterval(slideInterval);
        });
        
        sliderContainer.addEventListener('mouseleave', function() {
          startSlideInterval();
        });
        
        // Start automatic slide transition
        startSlideInterval();
      });
    </script> 


</body>

</html>